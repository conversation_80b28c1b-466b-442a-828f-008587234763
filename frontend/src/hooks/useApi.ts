/**
 * Custom hook for API calls with caching and error handling
 */

import { useState, useCallback, useRef } from 'react';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiOptions {
  cache?: boolean;
  cacheTime?: number; // in milliseconds
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

// Global cache for API responses
const apiCache = new Map<string, CacheEntry<any>>();

export function useApi<T = any>(options: UseApiOptions = {}) {
  const { cache = true, cacheTime = 5 * 60 * 1000 } = options; // 5 minutes default cache

  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  const generateCacheKey = useCallback((url: string, config?: AxiosRequestConfig) => {
    return `${url}_${JSON.stringify(config?.params || {})}_${config?.method || 'GET'}`;
  }, []);

  const isCacheValid = useCallback((timestamp: number) => {
    return Date.now() - timestamp < cacheTime;
  }, [cacheTime]);

  const request = useCallback(async (
    url: string,
    config?: AxiosRequestConfig
  ): Promise<T | null> => {
    // Cancel previous request if still pending
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    const cacheKey = generateCacheKey(url, config);

    // Check cache first
    if (cache && config?.method !== 'POST' && config?.method !== 'PUT' && config?.method !== 'DELETE') {
      const cachedEntry = apiCache.get(cacheKey);
      if (cachedEntry && isCacheValid(cachedEntry.timestamp)) {
        setState({
          data: cachedEntry.data,
          loading: false,
          error: null,
        });
        return cachedEntry.data;
      }
    }

    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
    }));

    try {
      const response: AxiosResponse<T> = await axios({
        url,
        signal: abortControllerRef.current.signal,
        ...config,
      });

      const responseData = response.data;

      // Cache successful GET requests
      if (cache && (!config?.method || config.method === 'GET')) {
        apiCache.set(cacheKey, {
          data: responseData,
          timestamp: Date.now(),
        });
      }

      setState({
        data: responseData,
        loading: false,
        error: null,
      });

      return responseData;
    } catch (error: any) {
      if (axios.isCancel(error)) {
        // Request was cancelled, don't update state
        return null;
      }

      const errorMessage = error.response?.data?.error || error.message || 'An error occurred';

      setState({
        data: null,
        loading: false,
        error: errorMessage,
      });

      throw error;
    }
  }, [cache, generateCacheKey, isCacheValid]);

  const clearCache = useCallback((url?: string, config?: AxiosRequestConfig) => {
    if (url) {
      const cacheKey = generateCacheKey(url, config);
      apiCache.delete(cacheKey);
    } else {
      apiCache.clear();
    }
  }, [generateCacheKey]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  // Cleanup on unmount
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  return {
    ...state,
    request,
    clearCache,
    reset,
    cleanup,
  };
}

export default useApi;
