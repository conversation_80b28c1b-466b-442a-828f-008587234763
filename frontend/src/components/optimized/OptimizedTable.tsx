/**
 * Optimized table component with virtualization and memoization
 */

import React, { memo, useMemo, useState, useCallback } from 'react';
import { Table, Form, But<PERSON>, Spinner } from 'react-bootstrap';

interface Column<T> {
  key: keyof T | string;
  header: string;
  render?: (item: T, index: number) => React.ReactNode;
  sortable?: boolean;
  width?: string;
}

interface OptimizedTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  pageSize?: number;
  searchable?: boolean;
  searchPlaceholder?: string;
  onRowClick?: (item: T, index: number) => void;
  className?: string;
  emptyMessage?: string;
  keyExtractor?: (item: T, index: number) => string | number;
}

type SortDirection = 'asc' | 'desc' | null;

interface SortState {
  column: string | null;
  direction: SortDirection;
}

function OptimizedTableComponent<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  pageSize = 50,
  searchable = false,
  searchPlaceholder = "Search...",
  onRowClick,
  className = "",
  emptyMessage = "No data available",
  keyExtractor = (_item, index) => index,
}: OptimizedTableProps<T>) {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortState, setSortState] = useState<SortState>({ column: null, direction: null });

  // Memoized filtered data
  const filteredData = useMemo(() => {
    if (!searchable || !searchTerm.trim()) {
      return data;
    }

    const searchLower = searchTerm.toLowerCase();
    return data.filter(item =>
      Object.values(item).some(value =>
        String(value).toLowerCase().includes(searchLower)
      )
    );
  }, [data, searchTerm, searchable]);

  // Memoized sorted data
  const sortedData = useMemo(() => {
    if (!sortState.column || !sortState.direction) {
      return filteredData;
    }

    return [...filteredData].sort((a, b) => {
      const aValue = a[sortState.column!];
      const bValue = b[sortState.column!];

      if (aValue === bValue) return 0;

      let comparison = 0;
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else {
        comparison = String(aValue).localeCompare(String(bValue));
      }

      return sortState.direction === 'asc' ? comparison : -comparison;
    });
  }, [filteredData, sortState]);

  // Memoized paginated data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return sortedData.slice(startIndex, endIndex);
  }, [sortedData, currentPage, pageSize]);

  // Memoized pagination info
  const paginationInfo = useMemo(() => {
    const totalItems = sortedData.length;
    const totalPages = Math.ceil(totalItems / pageSize);
    const startItem = (currentPage - 1) * pageSize + 1;
    const endItem = Math.min(currentPage * pageSize, totalItems);

    return {
      totalItems,
      totalPages,
      startItem,
      endItem,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    };
  }, [sortedData.length, currentPage, pageSize]);

  // Handlers
  const handleSort = useCallback((columnKey: string) => {
    setSortState(prev => {
      if (prev.column === columnKey) {
        // Cycle through: asc -> desc -> null
        const newDirection: SortDirection =
          prev.direction === 'asc' ? 'desc' :
            prev.direction === 'desc' ? null : 'asc';
        return { column: newDirection ? columnKey : null, direction: newDirection };
      } else {
        return { column: columnKey, direction: 'asc' };
      }
    });
  }, []);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  }, []);

  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const renderSortIcon = useCallback((columnKey: string) => {
    if (sortState.column !== columnKey) {
      return <span className="text-muted ms-1">↕</span>;
    }
    return sortState.direction === 'asc' ?
      <span className="text-primary ms-1">↑</span> :
      <span className="text-primary ms-1">↓</span>;
  }, [sortState]);

  const renderPagination = useCallback(() => {
    if (paginationInfo.totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(paginationInfo.totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <Button
          key={i}
          variant={i === currentPage ? "primary" : "outline-primary"}
          size="sm"
          onClick={() => handlePageChange(i)}
          className="me-1"
        >
          {i}
        </Button>
      );
    }

    return (
      <div className="d-flex justify-content-between align-items-center mt-3">
        <div className="text-muted">
          Showing {paginationInfo.startItem}-{paginationInfo.endItem} of {paginationInfo.totalItems} items
        </div>
        <div>
          <Button
            variant="outline-primary"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={!paginationInfo.hasPrevPage}
            className="me-2"
          >
            Previous
          </Button>
          {pages}
          <Button
            variant="outline-primary"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={!paginationInfo.hasNextPage}
            className="ms-1"
          >
            Next
          </Button>
        </div>
      </div>
    );
  }, [paginationInfo, currentPage, handlePageChange]);

  if (loading) {
    return (
      <div className="text-center py-4">
        <Spinner animation="border" />
        <div className="mt-2">Loading...</div>
      </div>
    );
  }

  return (
    <div className={className}>
      {searchable && (
        <div className="mb-3">
          <Form.Control
            type="text"
            placeholder={searchPlaceholder}
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
      )}

      <Table responsive hover className="mb-0">
        <thead>
          <tr>
            {columns.map((column, index) => (
              <th
                key={String(column.key) || index}
                style={{ width: column.width }}
                className={column.sortable ? "user-select-none cursor-pointer" : ""}
                onClick={column.sortable ? () => handleSort(String(column.key)) : undefined}
              >
                {column.header}
                {column.sortable && renderSortIcon(String(column.key))}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {paginatedData.length === 0 ? (
            <tr>
              <td colSpan={columns.length} className="text-center py-4 text-muted">
                {emptyMessage}
              </td>
            </tr>
          ) : (
            paginatedData.map((item, index) => (
              <tr
                key={keyExtractor(item, index)}
                className={onRowClick ? "cursor-pointer" : ""}
                onClick={onRowClick ? () => onRowClick(item, index) : undefined}
              >
                {columns.map((column, colIndex) => (
                  <td key={String(column.key) || colIndex}>
                    {column.render
                      ? column.render(item, index)
                      : String(item[column.key] || '-')
                    }
                  </td>
                ))}
              </tr>
            ))
          )}
        </tbody>
      </Table>

      {renderPagination()}
    </div>
  );
}

// Memoize the component to prevent unnecessary re-renders
export const OptimizedTable = memo(OptimizedTableComponent) as <T extends Record<string, any>>(
  props: OptimizedTableProps<T>
) => JSX.Element;

export default OptimizedTable;
