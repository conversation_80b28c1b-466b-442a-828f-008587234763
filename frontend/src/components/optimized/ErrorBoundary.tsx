/**
 * Error boundary component for better error handling and user experience
 */

import { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Container } from 'react-bootstrap';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error to console in development
    if (import.meta.env.DEV) {
      console.error('Error caught by boundary:', error);
      console.error('Error info:', errorInfo);
    }

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Container className="mt-4">
          <Card className="border-danger">
            <Card.Header className="bg-danger text-white">
              <h5 className="mb-0">
                <i className="bi bi-exclamation-triangle me-2"></i>
                Something went wrong
              </h5>
            </Card.Header>
            <Card.Body>
              <Alert variant="danger" className="mb-3">
                <Alert.Heading>Application Error</Alert.Heading>
                <p className="mb-0">
                  An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.
                </p>
              </Alert>

              {import.meta.env.DEV && this.state.error && (
                <div className="mt-3">
                  <h6>Error Details (Development Mode):</h6>
                  <pre className="bg-light p-3 rounded small text-danger">
                    {this.state.error.toString()}
                    {this.state.errorInfo?.componentStack}
                  </pre>
                </div>
              )}

              <div className="d-flex gap-2 mt-3">
                <Button variant="primary" onClick={this.handleRetry}>
                  <i className="bi bi-arrow-clockwise me-2"></i>
                  Try Again
                </Button>
                <Button
                  variant="outline-secondary"
                  onClick={() => window.location.reload()}
                >
                  <i className="bi bi-arrow-clockwise me-2"></i>
                  Refresh Page
                </Button>
              </div>
            </Card.Body>
          </Card>
        </Container>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
