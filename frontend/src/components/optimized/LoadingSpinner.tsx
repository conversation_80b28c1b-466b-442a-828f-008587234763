/**
 * Optimized loading spinner component with different variants
 */

import React, { memo } from 'react';
import { Spinner } from 'react-bootstrap';

interface LoadingSpinnerProps {
  variant?: 'border' | 'grow';
  size?: 'sm' | 'lg';
  color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  text?: string;
  fullScreen?: boolean;
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = memo(({
  variant = 'border',
  size,
  color = 'primary',
  text = 'Loading...',
  fullScreen = false,
  className = '',
}) => {
  const spinnerElement = (
    <div className={`text-center ${className}`}>
      <Spinner
        animation={variant}
        variant={color}
        size={size}
        role="status"
        aria-hidden="true"
      />
      {text && (
        <div className="mt-2 text-muted">
          {text}
        </div>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div 
        className="d-flex justify-content-center align-items-center position-fixed top-0 start-0 w-100 h-100"
        style={{ 
          backgroundColor: 'rgba(255, 255, 255, 0.8)', 
          zIndex: 9999,
          backdropFilter: 'blur(2px)'
        }}
      >
        {spinnerElement}
      </div>
    );
  }

  return (
    <div className="py-4">
      {spinnerElement}
    </div>
  );
});

LoadingSpinner.displayName = 'LoadingSpinner';

export default LoadingSpinner;
