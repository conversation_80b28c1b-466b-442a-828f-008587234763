"""
Optimized file handling service.
Implements efficient file operations following SOLID principles.
"""

import asyncio
import io
import os
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from typing import Dict, List, Optional, Union
import pandas as pd
from fastapi import UploadFile

from app.services.cache_service import cache_service, dataframe_cache
from app.config import BASE_DIR


class FileHandlerInterface:
    """Interface for file handlers (Interface Segregation Principle)"""
    
    async def read_file(self, file: UploadFile) -> bytes:
        raise NotImplementedError
    
    def process_file_content(self, content: bytes, file_type: str) -> pd.DataFrame:
        raise NotImplementedError


class OptimizedFileHandler:
    """
    Optimized file handler with async operations and caching.
    Follows Single Responsibility Principle - handles file operations efficiently.
    """

    def __init__(self, max_workers: int = 4, use_cache: bool = True):
        self.max_workers = max_workers
        self.use_cache = use_cache
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

    async def read_multiple_files_async(
        self, 
        files: List[Optional[UploadFile]]
    ) -> List[bytes]:
        """
        Read multiple files asynchronously for better performance.
        
        Args:
            files: List of UploadFile objects (can contain None values)
            
        Returns:
            List of file contents as bytes
        """
        # Filter out None files
        valid_files = [f for f in files if f is not None]
        
        if not valid_files:
            return []

        # Read files concurrently
        tasks = [self._read_single_file_async(file) for file in valid_files]
        contents = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and return valid contents
        valid_contents = []
        for content in contents:
            if isinstance(content, bytes):
                valid_contents.append(content)
            else:
                print(f"Error reading file: {content}")
        
        return valid_contents

    async def _read_single_file_async(self, file: UploadFile) -> bytes:
        """Read a single file asynchronously"""
        try:
            return await file.read()
        except Exception as e:
            print(f"Error reading file {file.filename}: {e}")
            raise

    def process_excel_files_optimized(
        self, 
        file_contents: List[bytes],
        dtype_dict: Optional[Dict[str, str]] = None
    ) -> List[pd.DataFrame]:
        """
        Process multiple Excel files with optimized reading.
        
        Args:
            file_contents: List of file contents as bytes
            dtype_dict: Optional data type specifications for columns
            
        Returns:
            List of processed DataFrames
        """
        if not file_contents:
            return []

        # Use caching for file processing
        cache_key = {
            "operation": "process_excel_files",
            "file_count": len(file_contents),
            "dtype_dict": dtype_dict or {}
        }

        if self.use_cache:
            cached_result = cache_service.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Process files in parallel using thread pool
        dataframes = []
        
        # Submit all tasks to thread pool
        futures = []
        for content in file_contents:
            future = self.executor.submit(
                self._process_single_excel_file, 
                content, 
                dtype_dict
            )
            futures.append(future)

        # Collect results
        for future in futures:
            try:
                df = future.result()
                if not df.empty:
                    dataframes.append(df)
            except Exception as e:
                print(f"Error processing Excel file: {e}")

        # Cache the result
        if self.use_cache:
            cache_service.set(cache_key, dataframes)

        return dataframes

    def _process_single_excel_file(
        self, 
        content: bytes, 
        dtype_dict: Optional[Dict[str, str]] = None
    ) -> pd.DataFrame:
        """Process a single Excel file with optimizations"""
        try:
            # Use optimized reading parameters
            read_params = {
                'engine': 'openpyxl',  # Generally faster for .xlsx files
                'na_filter': True,     # Enable NA filtering
            }
            
            if dtype_dict:
                read_params['dtype'] = dtype_dict

            df = pd.read_excel(io.BytesIO(content), **read_params)
            
            # Basic optimization - convert object columns to category where appropriate
            for col in df.select_dtypes(include=['object']).columns:
                if df[col].nunique() / len(df) < 0.5:  # Less than 50% unique values
                    df[col] = df[col].astype('category')
            
            return df
            
        except Exception as e:
            print(f"Error processing single Excel file: {e}")
            return pd.DataFrame()

    def save_dataframe_optimized(
        self, 
        df: pd.DataFrame, 
        file_path: str,
        format_type: str = "parquet"
    ) -> bool:
        """
        Save DataFrame using optimized format and compression.
        
        Args:
            df: DataFrame to save
            file_path: Path to save the file
            format_type: Format to use (parquet, csv, excel)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure directory exists
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            if format_type.lower() == "parquet":
                # Parquet is generally the most efficient format
                df.to_parquet(file_path, index=False, compression='snappy')
            elif format_type.lower() == "csv":
                df.to_csv(file_path, index=False)
            elif format_type.lower() == "excel":
                df.to_excel(file_path, index=False, engine='openpyxl')
            else:
                raise ValueError(f"Unsupported format: {format_type}")
            
            return True
            
        except Exception as e:
            print(f"Error saving DataFrame: {e}")
            return False

    def load_dataframe_optimized(
        self, 
        file_path: str,
        format_type: str = "auto"
    ) -> Optional[pd.DataFrame]:
        """
        Load DataFrame using optimized reading.
        
        Args:
            file_path: Path to the file
            format_type: Format type (auto, parquet, csv, excel)
            
        Returns:
            DataFrame if successful, None otherwise
        """
        try:
            if not os.path.exists(file_path):
                return None

            # Auto-detect format if not specified
            if format_type == "auto":
                file_ext = Path(file_path).suffix.lower()
                if file_ext == ".parquet":
                    format_type = "parquet"
                elif file_ext == ".csv":
                    format_type = "csv"
                elif file_ext in [".xlsx", ".xls"]:
                    format_type = "excel"
                else:
                    raise ValueError(f"Cannot auto-detect format for {file_ext}")

            # Use caching for file loading
            cache_key = {
                "operation": "load_dataframe",
                "file_path": file_path,
                "file_mtime": os.path.getmtime(file_path),
                "format_type": format_type
            }

            if self.use_cache:
                cached_result = dataframe_cache.get(cache_key)
                if cached_result is not None:
                    return cached_result

            # Load the file
            if format_type == "parquet":
                df = pd.read_parquet(file_path)
            elif format_type == "csv":
                df = pd.read_csv(file_path)
            elif format_type == "excel":
                df = pd.read_excel(file_path, engine='openpyxl')
            else:
                raise ValueError(f"Unsupported format: {format_type}")

            # Cache the result
            if self.use_cache:
                dataframe_cache.set(cache_key, df)

            return df
            
        except Exception as e:
            print(f"Error loading DataFrame from {file_path}: {e}")
            return None

    def cleanup_temp_files(self, temp_dir: str = None) -> int:
        """
        Clean up temporary files older than specified age.
        
        Args:
            temp_dir: Directory to clean (defaults to temp directory in BASE_DIR)
            
        Returns:
            Number of files deleted
        """
        if temp_dir is None:
            temp_dir = os.path.join(BASE_DIR, "temp")

        if not os.path.exists(temp_dir):
            return 0

        deleted_count = 0
        try:
            temp_path = Path(temp_dir)
            
            # Delete files older than 24 hours
            import time
            current_time = time.time()
            
            for file_path in temp_path.rglob("*"):
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > 86400:  # 24 hours in seconds
                        file_path.unlink()
                        deleted_count += 1
                        
        except Exception as e:
            print(f"Error cleaning temp files: {e}")
            
        return deleted_count

    def get_file_info(self, file_path: str) -> Optional[Dict]:
        """
        Get optimized file information.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file information
        """
        try:
            if not os.path.exists(file_path):
                return None

            file_stat = os.stat(file_path)
            file_path_obj = Path(file_path)
            
            return {
                "name": file_path_obj.name,
                "size": file_stat.st_size,
                "modified": file_stat.st_mtime,
                "extension": file_path_obj.suffix.lower(),
                "is_readable": os.access(file_path, os.R_OK),
                "is_writable": os.access(file_path, os.W_OK)
            }
            
        except Exception as e:
            print(f"Error getting file info for {file_path}: {e}")
            return None

    def __del__(self):
        """Cleanup executor on deletion"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)


# Global file handler instance
file_handler = OptimizedFileHandler()
