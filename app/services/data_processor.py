"""
Optimized data processing service.
Implements efficient data processing following SOLID principles.
"""

import io
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Optional, Union
import pandas as pd
import numpy as np

from app.services.cache_service import dataframe_cache
from app.utils import format_currency


class DataProcessorInterface:
    """Interface for data processors (Interface Segregation Principle)"""
    
    def process_data(self, data: pd.DataFrame) -> pd.DataFrame:
        raise NotImplementedError


class OptimizedDataProcessor:
    """
    Optimized data processor with caching and parallel processing.
    Follows Single Responsibility Principle - handles data processing optimization.
    """

    def __init__(self, use_cache: bool = True, max_workers: int = 4):
        self.use_cache = use_cache
        self.max_workers = max_workers

    def process_multiple_files_parallel(
        self, 
        file_contents: List[bytes], 
        processor_func: callable,
        file_type: str = "excel"
    ) -> pd.DataFrame:
        """
        Process multiple files in parallel for better performance.
        
        Args:
            file_contents: List of file contents as bytes
            processor_func: Function to process each file
            file_type: Type of file (excel, csv)
            
        Returns:
            Combined DataFrame from all files
        """
        if not file_contents:
            return pd.DataFrame()

        # Use caching for file processing
        cache_key = {
            "operation": "process_multiple_files",
            "file_count": len(file_contents),
            "file_type": file_type,
            "processor": processor_func.__name__
        }

        if self.use_cache:
            cached_result = dataframe_cache.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Process files in parallel
        dataframes = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all file processing tasks
            future_to_content = {
                executor.submit(self._process_single_file, content, processor_func, file_type): content
                for content in file_contents
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_content):
                try:
                    df = future.result()
                    if not df.empty:
                        dataframes.append(df)
                except Exception as e:
                    print(f"Error processing file: {e}")

        # Combine all DataFrames efficiently
        if dataframes:
            result = pd.concat(dataframes, ignore_index=True)
            
            # Cache the result
            if self.use_cache:
                dataframe_cache.set(cache_key, result)
                
            return result
        
        return pd.DataFrame()

    def _process_single_file(
        self, 
        file_content: bytes, 
        processor_func: callable,
        file_type: str
    ) -> pd.DataFrame:
        """Process a single file"""
        try:
            if file_type.lower() == "excel":
                df = pd.read_excel(io.BytesIO(file_content))
            elif file_type.lower() == "csv":
                df = pd.read_csv(io.BytesIO(file_content))
            else:
                raise ValueError(f"Unsupported file type: {file_type}")
            
            return processor_func(df)
        except Exception as e:
            print(f"Error processing single file: {e}")
            return pd.DataFrame()

    def optimize_dataframe_memory(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Optimize DataFrame memory usage by converting data types.
        
        Args:
            df: Input DataFrame
            
        Returns:
            Memory-optimized DataFrame
        """
        if df.empty:
            return df

        df_optimized = df.copy()
        
        # Optimize numeric columns
        for col in df_optimized.select_dtypes(include=[np.number]).columns:
            col_min = df_optimized[col].min()
            col_max = df_optimized[col].max()
            
            if df_optimized[col].dtype == 'int64':
                if col_min >= np.iinfo(np.int8).min and col_max <= np.iinfo(np.int8).max:
                    df_optimized[col] = df_optimized[col].astype(np.int8)
                elif col_min >= np.iinfo(np.int16).min and col_max <= np.iinfo(np.int16).max:
                    df_optimized[col] = df_optimized[col].astype(np.int16)
                elif col_min >= np.iinfo(np.int32).min and col_max <= np.iinfo(np.int32).max:
                    df_optimized[col] = df_optimized[col].astype(np.int32)
            
            elif df_optimized[col].dtype == 'float64':
                if col_min >= np.finfo(np.float32).min and col_max <= np.finfo(np.float32).max:
                    df_optimized[col] = df_optimized[col].astype(np.float32)

        # Optimize string columns to category where appropriate
        for col in df_optimized.select_dtypes(include=['object']).columns:
            if df_optimized[col].nunique() / len(df_optimized) < 0.5:  # Less than 50% unique values
                df_optimized[col] = df_optimized[col].astype('category')

        return df_optimized

    def efficient_groupby_operations(
        self, 
        df: pd.DataFrame, 
        group_cols: List[str], 
        agg_dict: Dict[str, Union[str, List[str]]]
    ) -> pd.DataFrame:
        """
        Perform efficient groupby operations with optimizations.
        
        Args:
            df: Input DataFrame
            group_cols: Columns to group by
            agg_dict: Aggregation dictionary
            
        Returns:
            Grouped and aggregated DataFrame
        """
        if df.empty:
            return df

        # Use categorical data types for grouping columns if beneficial
        df_grouped = df.copy()
        for col in group_cols:
            if col in df_grouped.columns and df_grouped[col].dtype == 'object':
                if df_grouped[col].nunique() / len(df_grouped) < 0.5:
                    df_grouped[col] = df_grouped[col].astype('category')

        # Perform groupby operation
        try:
            result = df_grouped.groupby(group_cols, observed=True).agg(agg_dict).reset_index()
            return result
        except Exception as e:
            print(f"Error in groupby operation: {e}")
            return pd.DataFrame()

    def batch_process_data(
        self, 
        df: pd.DataFrame, 
        batch_size: int = 10000,
        process_func: callable = None
    ) -> pd.DataFrame:
        """
        Process large DataFrames in batches to manage memory usage.
        
        Args:
            df: Input DataFrame
            batch_size: Size of each batch
            process_func: Function to apply to each batch
            
        Returns:
            Processed DataFrame
        """
        if df.empty or len(df) <= batch_size:
            return process_func(df) if process_func else df

        processed_batches = []
        
        for start_idx in range(0, len(df), batch_size):
            end_idx = min(start_idx + batch_size, len(df))
            batch = df.iloc[start_idx:end_idx].copy()
            
            if process_func:
                processed_batch = process_func(batch)
            else:
                processed_batch = batch
                
            processed_batches.append(processed_batch)

        return pd.concat(processed_batches, ignore_index=True)


class TelcoDataProcessor(OptimizedDataProcessor):
    """
    Specialized processor for telco data.
    Extends OptimizedDataProcessor following Open/Closed Principle.
    """

    def process_telco_summary_data(
        self, 
        hisa_df: pd.DataFrame, 
        target_date: str,
        telcos: List[str]
    ) -> Dict[str, Dict]:
        """
        Efficiently process telco summary data with caching and optimization.
        
        Args:
            hisa_df: HISA DataFrame
            target_date: Target date for analysis
            telcos: List of telco names
            
        Returns:
            Dictionary with telco summaries
        """
        cache_key = {
            "operation": "telco_summary",
            "target_date": target_date,
            "data_hash": hash(str(hisa_df.shape) + str(hisa_df.columns.tolist())),
            "telcos": sorted(telcos)
        }

        if self.use_cache:
            cached_result = dataframe_cache.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Optimize DataFrame memory usage
        hisa_optimized = self.optimize_dataframe_memory(hisa_df)
        
        # Filter data for target date once
        target_date_obj = pd.to_datetime(target_date).date()
        hisa_day = hisa_optimized[hisa_optimized["Date"].dt.date == target_date_obj].copy()

        summary = {}
        
        # Process each telco efficiently
        for telco in telcos:
            telco_data = hisa_day[hisa_day["Network"].str.upper() == telco].copy()
            
            if telco_data.empty:
                summary[telco] = self._create_empty_summary()
                continue

            # Use efficient groupby operations
            status_summary = self._analyze_by_status_optimized(telco_data)
            type_summary = self._analyze_by_type_optimized(telco_data)

            summary[telco] = {
                "total_transactions": len(telco_data),
                "total_value": format_currency(telco_data["Amount"].sum()),
                "by_status": status_summary,
                "by_type": type_summary,
            }

        # Cache the result
        if self.use_cache:
            dataframe_cache.set(cache_key, summary)

        return summary

    def _analyze_by_status_optimized(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """Optimized status analysis using vectorized operations"""
        status_groups = df.groupby("TransactionStatus", observed=True).agg({
            "Amount": ["count", "sum"]
        }).round(2)
        
        status_summary = {}
        for status in ["SUCCESS", "FAILED"]:
            if status in status_groups.index:
                count = int(status_groups.loc[status, ("Amount", "count")])
                value = float(status_groups.loc[status, ("Amount", "sum")])
            else:
                count = 0
                value = 0.0
            
            status_summary[status] = {
                "count": count,
                "value": format_currency(value)
            }
        
        return status_summary

    def _analyze_by_type_optimized(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """Optimized type analysis using vectorized operations"""
        type_groups = df.groupby("TransactionType", observed=True).agg({
            "Amount": ["count", "sum"]
        }).round(2)
        
        type_summary = {}
        for tx_type in ["AIRTIME", "DATA"]:
            if tx_type in type_groups.index:
                count = int(type_groups.loc[tx_type, ("Amount", "count")])
                value = float(type_groups.loc[tx_type, ("Amount", "sum")])
            else:
                count = 0
                value = 0.0
            
            type_summary[tx_type] = {
                "count": count,
                "value": format_currency(value)
            }
        
        return type_summary

    def _create_empty_summary(self) -> Dict:
        """Create empty summary for telcos with no data"""
        return {
            "total_transactions": 0,
            "total_value": format_currency(0),
            "by_status": {
                "SUCCESS": {"count": 0, "value": format_currency(0)},
                "FAILED": {"count": 0, "value": format_currency(0)},
            },
            "by_type": {
                "AIRTIME": {"count": 0, "value": format_currency(0)},
                "DATA": {"count": 0, "value": format_currency(0)},
            },
        }


# Global processor instance
telco_processor = TelcoDataProcessor()
