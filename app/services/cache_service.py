"""
Caching service for improved performance.
Implements caching strategies following SOLID principles.
"""

import hashlib
import json
import os
import pickle
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union
from pathlib import Path
import pandas as pd

from app.config import BASE_DIR


class CacheService:
    """
    Cache service for storing and retrieving processed data.
    Follows Single Responsibility Principle - only handles caching operations.
    """

    def __init__(self, cache_dir: str = None):
        self.cache_dir = Path(cache_dir or os.path.join(BASE_DIR, "cache"))
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.default_ttl = timedelta(hours=24)  # Default cache TTL

    def _generate_cache_key(self, key_data: Union[str, Dict]) -> str:
        """Generate a unique cache key from input data"""
        if isinstance(key_data, str):
            content = key_data
        else:
            content = json.dumps(key_data, sort_keys=True)
        
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cache_path(self, cache_key: str) -> Path:
        """Get the full path for a cache file"""
        return self.cache_dir / f"{cache_key}.cache"

    def _is_cache_valid(self, cache_path: Path, ttl: timedelta = None) -> bool:
        """Check if cache file exists and is within TTL"""
        if not cache_path.exists():
            return False
        
        ttl = ttl or self.default_ttl
        file_time = datetime.fromtimestamp(cache_path.stat().st_mtime)
        return datetime.now() - file_time < ttl

    def get(self, key: Union[str, Dict], ttl: timedelta = None) -> Optional[Any]:
        """
        Retrieve data from cache if valid.
        
        Args:
            key: Cache key (string or dict that will be hashed)
            ttl: Time to live for cache validity
            
        Returns:
            Cached data if valid, None otherwise
        """
        try:
            cache_key = self._generate_cache_key(key)
            cache_path = self._get_cache_path(cache_key)
            
            if not self._is_cache_valid(cache_path, ttl):
                return None
            
            with open(cache_path, 'rb') as f:
                return pickle.load(f)
                
        except Exception as e:
            print(f"Cache get error: {e}")
            return None

    def set(self, key: Union[str, Dict], data: Any) -> bool:
        """
        Store data in cache.
        
        Args:
            key: Cache key (string or dict that will be hashed)
            data: Data to cache
            
        Returns:
            True if successful, False otherwise
        """
        try:
            cache_key = self._generate_cache_key(key)
            cache_path = self._get_cache_path(cache_key)
            
            with open(cache_path, 'wb') as f:
                pickle.dump(data, f)
            
            return True
            
        except Exception as e:
            print(f"Cache set error: {e}")
            return False

    def delete(self, key: Union[str, Dict]) -> bool:
        """Delete cached data"""
        try:
            cache_key = self._generate_cache_key(key)
            cache_path = self._get_cache_path(cache_key)
            
            if cache_path.exists():
                cache_path.unlink()
            
            return True
            
        except Exception as e:
            print(f"Cache delete error: {e}")
            return False

    def clear_expired(self) -> int:
        """Clear all expired cache files and return count of deleted files"""
        deleted_count = 0
        
        try:
            for cache_file in self.cache_dir.glob("*.cache"):
                if not self._is_cache_valid(cache_file):
                    cache_file.unlink()
                    deleted_count += 1
                    
        except Exception as e:
            print(f"Cache clear expired error: {e}")
            
        return deleted_count

    def clear_all(self) -> int:
        """Clear all cache files and return count of deleted files"""
        deleted_count = 0
        
        try:
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink()
                deleted_count += 1
                
        except Exception as e:
            print(f"Cache clear all error: {e}")
            
        return deleted_count


class DataFrameCacheService(CacheService):
    """
    Specialized cache service for pandas DataFrames.
    Extends CacheService following Open/Closed Principle.
    """

    def _get_cache_path(self, cache_key: str) -> Path:
        """Override to use .parquet extension for DataFrames"""
        return self.cache_dir / f"{cache_key}.parquet"

    def get(self, key: Union[str, Dict], ttl: timedelta = None) -> Optional[pd.DataFrame]:
        """Get DataFrame from cache"""
        try:
            cache_key = self._generate_cache_key(key)
            cache_path = self._get_cache_path(cache_key)
            
            if not self._is_cache_valid(cache_path, ttl):
                return None
            
            return pd.read_parquet(cache_path)
            
        except Exception as e:
            print(f"DataFrame cache get error: {e}")
            return None

    def set(self, key: Union[str, Dict], data: pd.DataFrame) -> bool:
        """Store DataFrame in cache using parquet format for efficiency"""
        try:
            cache_key = self._generate_cache_key(key)
            cache_path = self._get_cache_path(cache_key)
            
            data.to_parquet(cache_path, index=False)
            return True
            
        except Exception as e:
            print(f"DataFrame cache set error: {e}")
            return False

    def clear_expired(self) -> int:
        """Clear expired parquet cache files"""
        deleted_count = 0
        
        try:
            for cache_file in self.cache_dir.glob("*.parquet"):
                if not self._is_cache_valid(cache_file):
                    cache_file.unlink()
                    deleted_count += 1
                    
        except Exception as e:
            print(f"DataFrame cache clear expired error: {e}")
            
        return deleted_count

    def clear_all(self) -> int:
        """Clear all parquet cache files"""
        deleted_count = 0
        
        try:
            for cache_file in self.cache_dir.glob("*.parquet"):
                cache_file.unlink()
                deleted_count += 1
                
        except Exception as e:
            print(f"DataFrame cache clear all error: {e}")
            
        return deleted_count


# Global cache instances
cache_service = CacheService()
dataframe_cache = DataFrameCacheService()
