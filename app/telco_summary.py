"""
Telco Summary Service Module

This module provides functionality for generating telco summaries with or without telco files.
It follows SOLID principles for clean architecture and maintainability.

Single Responsibility Principle: Each class has a single, well-defined responsibility
Open/Closed Principle: Classes are open for extension but closed for modification
Liskov Substitution Principle: Derived classes can be substituted for their base classes
Interface Segregation Principle: Interfaces are specific to client needs
Dependency Inversion Principle: High-level modules don't depend on low-level modules
"""

import io
import os
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Union
import pandas as pd

from app import clean_merge_hisa, reconcile_logs
from app.airtel import prepare_airtel_telco
from app.glo import prepare_glo_telco
from app.mtn import prepare_mtn_telco
from app.utils import format_currency, convert_kobo_to_naira
from app.managers import HisaSFTPManager
from app.config import BASE_DIR
from app.services.cache_service import dataframe_cache
from app.services.data_processor import telco_processor
from app.services.file_handler import file_handler


class TelcoProcessor(ABC):
    """Abstract base class for telco data processors (Interface Segregation)"""

    @abstractmethod
    def process_telco_data(self, df: pd.DataFrame) -> Union[pd.DataFrame, str]:
        """Process telco data and return cleaned DataFrame or error message"""
        pass

    @abstractmethod
    def get_telco_name(self) -> str:
        """Get the telco name"""
        pass


class MTNProcessor(TelcoProcessor):
    """MTN telco data processor"""

    def process_telco_data(self, df: pd.DataFrame) -> Union[pd.DataFrame, str]:
        return prepare_mtn_telco(df)

    def get_telco_name(self) -> str:
        return "MTN"


class AirtelProcessor(TelcoProcessor):
    """Airtel telco data processor"""

    def process_telco_data(self, df: pd.DataFrame) -> Union[pd.DataFrame, str]:
        return prepare_airtel_telco(df)

    def get_telco_name(self) -> str:
        return "AIRTEL"


class GLOProcessor(TelcoProcessor):
    """GLO telco data processor"""

    def process_telco_data(self, df: pd.DataFrame) -> Union[pd.DataFrame, str]:
        return prepare_glo_telco(df)

    def get_telco_name(self) -> str:
        return "GLO"


class TelcoProcessorFactory:
    """Factory for creating telco processors (Dependency Inversion)"""

    _processors = {
        "MTN": MTNProcessor,
        "AIRTEL": AirtelProcessor,
        "GLO": GLOProcessor,
    }

    @classmethod
    def create_processor(cls, telco_name: str) -> TelcoProcessor:
        """Create a telco processor for the given telco name"""
        processor_class = cls._processors.get(telco_name.upper())
        if not processor_class:
            raise ValueError(f"Unsupported telco: {telco_name}")
        return processor_class()

    @classmethod
    def get_supported_telcos(cls) -> List[str]:
        """Get list of supported telco names"""
        return list(cls._processors.keys())


class HisaLogProcessor:
    """Processor for HISA CSV logs (Single Responsibility)"""

    def __init__(self):
        self.sftp_manager = HisaSFTPManager()

    def get_hisa_logs_for_date(self, target_date: str) -> Dict[str, pd.DataFrame]:
        """
        Get HISA transaction logs for both hisa_one and hisa_two sources for the target date.

        Args:
            target_date: Date in YYYY-MM-DD format

        Returns:
            Dict with 'hisa_one' and 'hisa_two' DataFrames, or empty DataFrames if not found
        """
        result = {"hisa_one": pd.DataFrame(), "hisa_two": pd.DataFrame()}

        for hisa_source in ["hisa_one", "hisa_two"]:
            try:
                # First check if any transaction files exist locally (skip SFTP if they do)
                base_path = f"{BASE_DIR}/reports/hisa_logs/{hisa_source}/{target_date}"
                transaction_files = [
                    f"{target_date}_airtime_transactions.csv",
                    f"{target_date}_bill_transactions.csv",
                    f"{target_date}_credit_transactions.csv",
                ]

                local_files_exist = any(
                    os.path.exists(f"{base_path}/{file_name}")
                    for file_name in transaction_files
                )

                if not local_files_exist:
                    # Try to download logs using SFTP
                    try:
                        local_check = self.sftp_manager.check_logs_exist_locally(
                            target_date,
                            hisa_one=(hisa_source == "hisa_one"),
                            hisa_two=(hisa_source == "hisa_two"),
                        )

                        if not local_check["exists"]:
                            if hisa_source == "hisa_one":
                                sftp_client = self.sftp_manager.connect(hisa_one=True)
                                download_result = self.sftp_manager.download_logs(
                                    sftp_client, target_date, hisa_one=True
                                )
                            else:
                                sftp_client = self.sftp_manager.connect(hisa_two=True)
                                download_result = self.sftp_manager.download_logs(
                                    sftp_client, target_date, hisa_two=True
                                )

                            sftp_client.close()

                            if "Failed" in download_result:
                                continue  # Skip this source if download failed
                    except Exception as sftp_error:
                        print(f"SFTP error for {hisa_source}: {sftp_error}")
                        continue  # Skip SFTP and try to use local files

                # Load and combine all transaction CSV files
                base_path = f"{BASE_DIR}/reports/hisa_logs/{hisa_source}/{target_date}"
                transaction_files = [
                    f"{target_date}_airtime_transactions.csv",
                    f"{target_date}_bill_transactions.csv",
                    f"{target_date}_credit_transactions.csv",
                ]

                combined_dfs = []
                for file_name in transaction_files:
                    csv_path = f"{base_path}/{file_name}"
                    if os.path.exists(csv_path):
                        df = pd.read_csv(csv_path)
                        if not df.empty:
                            # Add transaction type based on file name
                            if "airtime" in file_name:
                                df["transaction_type"] = "AIRTIME"
                            elif "bill" in file_name:
                                df["transaction_type"] = (
                                    "DATA"  # Bills are typically data transactions
                                )
                            elif "credit" in file_name:
                                df["transaction_type"] = "CREDIT"

                            # Ensure consistent column types and handle duplicates
                            df = df.loc[
                                :, ~df.columns.duplicated()
                            ]  # Remove duplicate columns
                            combined_dfs.append(df)

                if combined_dfs:
                    # Combine all transaction types
                    combined_df = pd.concat(combined_dfs, ignore_index=True, sort=False)
                    # Process the CSV data to match expected format
                    processed_df = self._process_csv_data(combined_df)
                    result[hisa_source] = processed_df

            except Exception as e:
                print(f"Error processing {hisa_source} logs: {e}")
                continue

        return result

    def get_hisa_logs_for_date_optimized(
        self, target_date: str
    ) -> Dict[str, pd.DataFrame]:
        """
        Optimized version of get_hisa_logs_for_date with caching and parallel processing.
        """
        # Use caching for HISA logs
        cache_key = {"operation": "get_hisa_logs_optimized", "target_date": target_date}

        cached_result = dataframe_cache.get(cache_key)
        if cached_result is not None:
            return cached_result

        result = self.get_hisa_logs_for_date(target_date)

        # Optimize DataFrames for memory usage
        for source, df in result.items():
            if not df.empty:
                result[source] = telco_processor.optimize_dataframe_memory(df)

        # Cache the optimized result
        dataframe_cache.set(cache_key, result)
        return result

    def _process_csv_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process CSV data to match the expected HISA format"""
        if df.empty:
            return df

        # Create a copy to avoid modifying original
        processed_df = df.copy()

        # Map CSV columns to expected format (handle different CSV structures)
        column_mapping = {
            "network_id": "Network",
            "transaction_id": "TxnId",
            "phone": "MSISDN",  # For airtime transactions
            "msisdn": "MSISDN",  # Alternative column name
            "amount": "Amount",
            "created_at": "Date",
            "status": "TransactionStatus",
            "transaction_type": "TransactionType",
            "user_id": "User",
        }

        # Rename columns if they exist (avoid conflicts)
        rename_dict = {}
        for csv_col, expected_col in column_mapping.items():
            if (
                csv_col in processed_df.columns
                and expected_col not in processed_df.columns
            ):
                rename_dict[csv_col] = expected_col

        if rename_dict:
            processed_df = processed_df.rename(columns=rename_dict)

        # Handle transaction type - use transaction_type if available, otherwise use type column
        if "TransactionType" not in processed_df.columns:
            if "type" in processed_df.columns:
                processed_df["TransactionType"] = processed_df["type"]
            else:
                processed_df["TransactionType"] = "UNKNOWN"

        # Convert network_id to network name or infer from phone number
        if "Network" in processed_df.columns:
            network_mapping = {1: "MTN", 2: "AIRTEL", 4: "GLO"}
            processed_df["Network"] = (
                processed_df["Network"].map(network_mapping).fillna("UNKNOWN")
            )
        else:
            # For transactions without network_id, try to infer from phone number
            processed_df["Network"] = "UNKNOWN"
            if "MSISDN" in processed_df.columns:
                # Simple heuristic based on phone prefixes (this is basic and may need refinement)
                processed_df.loc[
                    processed_df["MSISDN"].str.contains(
                        "0803|0806|0810|0813|0814|0816|0903|0906|0913|0916", na=False
                    ),
                    "Network",
                ] = "MTN"
                processed_df.loc[
                    processed_df["MSISDN"].str.contains(
                        "0802|0808|0812|0901|0902|0904|0907|0912", na=False
                    ),
                    "Network",
                ] = "AIRTEL"
                processed_df.loc[
                    processed_df["MSISDN"].str.contains(
                        "0805|0807|0811|0815|0905|0915", na=False
                    ),
                    "Network",
                ] = "GLO"

        # Convert amount from Kobo to Naira if needed
        if "Amount" in processed_df.columns:
            processed_df["Amount"] = pd.to_numeric(
                processed_df["Amount"], errors="coerce"
            ).fillna(0)
            processed_df["Amount"] = processed_df["Amount"].apply(convert_kobo_to_naira)

        # Convert date column
        if "Date" in processed_df.columns:
            processed_df["Date"] = pd.to_datetime(processed_df["Date"], errors="coerce")

        # Standardize status values
        if "TransactionStatus" in processed_df.columns:
            processed_df["TransactionStatus"] = processed_df[
                "TransactionStatus"
            ].str.upper()
            processed_df["TransactionStatus"] = processed_df[
                "TransactionStatus"
            ].replace({"SUCCESSFUL": "SUCCESS", "FAILED": "FAILED"})

        # Ensure required columns exist
        required_columns = [
            "Network",
            "TxnId",
            "MSISDN",
            "Amount",
            "Date",
            "TransactionStatus",
            "TransactionType",
            "User",
        ]
        for col in required_columns:
            if col not in processed_df.columns:
                if col in ["Network", "TransactionStatus", "TransactionType"]:
                    processed_df[col] = "UNKNOWN"
                elif col == "MSISDN":
                    processed_df[col] = (
                        "N/A"  # Some transactions may not have phone numbers
                    )
                elif col == "Amount":
                    processed_df[col] = 0
                else:
                    processed_df[col] = ""

        return processed_df[required_columns]


class HisaDataAnalyzer:
    """Analyzer for HISA data (Single Responsibility)"""

    def analyze_by_telco(
        self, hisa_df: pd.DataFrame, target_date: str
    ) -> Dict[str, Dict]:
        """Analyze HISA data by telco for the target date"""
        # Use optimized processor for better performance
        return telco_processor.process_telco_summary_data(
            hisa_df, target_date, TelcoProcessorFactory.get_supported_telcos()
        )

    def analyze_by_telco_original(
        self, hisa_df: pd.DataFrame, target_date: str
    ) -> Dict[str, Dict]:
        """Original analyze_by_telco method (kept for compatibility)"""
        target_date_obj = pd.to_datetime(target_date).date()
        hisa_day = hisa_df[hisa_df["Date"].dt.date == target_date_obj].copy()

        summary = {}

        for telco in TelcoProcessorFactory.get_supported_telcos():
            telco_data = hisa_day[hisa_day["Network"].str.upper() == telco].copy()

            if telco_data.empty:
                summary[telco] = self._create_empty_summary()
                continue

            # Analyze by transaction status
            status_summary = self._analyze_by_status(telco_data)

            # Analyze by transaction type
            type_summary = self._analyze_by_type(telco_data)

            summary[telco] = {
                "total_transactions": len(telco_data),
                "total_value": format_currency(telco_data["Amount"].sum()),
                "by_status": status_summary,
                "by_type": type_summary,
            }

        return summary

    def analyze_user_consumption_by_telco(
        self, hisa_df: pd.DataFrame, target_date: str, telco: str
    ) -> Dict[str, Dict]:
        """Analyze user consumption details for a specific telco"""
        target_date_obj = pd.to_datetime(target_date).date()
        hisa_day = hisa_df[hisa_df["Date"].dt.date == target_date_obj].copy()

        # Filter by telco
        telco_data = hisa_day[hisa_day["Network"].str.upper() == telco.upper()].copy()

        if telco_data.empty:
            return {"users": [], "summary": self._create_empty_summary()}

        # Group by User to get consumption details
        user_consumption = (
            telco_data.groupby(["User", "Source"])
            .agg(
                {
                    "Amount": ["sum", "count"],
                    "TransactionStatus": lambda x: (x == "SUCCESS").sum(),
                    "TransactionType": lambda x: list(x),
                    "MSISDN": lambda x: list(x.unique()),
                }
            )
            .reset_index()
        )

        # Flatten column names
        user_consumption.columns = [
            "User",
            "Source",
            "TotalAmount",
            "TransactionCount",
            "SuccessfulTransactions",
            "TransactionTypes",
            "MSISDNs",
        ]

        # Calculate success rate
        user_consumption["SuccessRate"] = (
            user_consumption["SuccessfulTransactions"]
            / user_consumption["TransactionCount"]
            * 100
        ).round(2)

        # Get detailed breakdown by transaction type for each user
        user_details = []
        for _, user_row in user_consumption.iterrows():
            user_id = user_row["User"]
            source = user_row["Source"]

            # Get user's transactions
            user_transactions = telco_data[
                (telco_data["User"] == user_id) & (telco_data["Source"] == source)
            ]

            # Breakdown by transaction type
            type_breakdown = {}
            for tx_type in ["AIRTIME", "DATA"]:
                type_data = user_transactions[
                    user_transactions["TransactionType"] == tx_type
                ]
                if not type_data.empty:
                    type_breakdown[tx_type] = {
                        "count": len(type_data),
                        "value": format_currency(type_data["Amount"].sum()),
                        "successful_count": len(
                            type_data[type_data["TransactionStatus"] == "SUCCESS"]
                        ),
                        "success_rate": (
                            round(
                                len(
                                    type_data[
                                        type_data["TransactionStatus"] == "SUCCESS"
                                    ]
                                )
                                / len(type_data)
                                * 100,
                                2,
                            )
                            if len(type_data) > 0
                            else 0
                        ),
                    }
                else:
                    type_breakdown[tx_type] = {
                        "count": 0,
                        "value": format_currency(0),
                        "successful_count": 0,
                        "success_rate": 0,
                    }

            # Breakdown by status
            status_breakdown = {}
            for status in ["SUCCESS", "FAILED"]:
                status_data = user_transactions[
                    user_transactions["TransactionStatus"] == status
                ]
                status_breakdown[status] = {
                    "count": len(status_data),
                    "value": format_currency(status_data["Amount"].sum()),
                }

            user_details.append(
                {
                    "user_id": user_id,
                    "source": source,
                    "total_transactions": int(user_row["TransactionCount"]),
                    "total_value": format_currency(user_row["TotalAmount"]),
                    "successful_transactions": int(user_row["SuccessfulTransactions"]),
                    "success_rate": float(user_row["SuccessRate"]),
                    "unique_msisdns": len(user_row["MSISDNs"]),
                    "msisdns": user_row["MSISDNs"][
                        :10
                    ],  # Limit to first 10 for display
                    "by_type": type_breakdown,
                    "by_status": status_breakdown,
                }
            )

        # Sort by total value descending
        user_details.sort(
            key=lambda x: float(x["total_value"].replace("₦", "").replace(",", "")),
            reverse=True,
        )

        # Overall summary for this telco
        overall_summary = {
            "total_transactions": len(telco_data),
            "total_value": format_currency(telco_data["Amount"].sum()),
            "total_users": len(user_consumption),
            "by_status": self._analyze_by_status(telco_data),
            "by_type": self._analyze_by_type(telco_data),
        }

        return {"users": user_details, "summary": overall_summary}

    def _analyze_by_status(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """Analyze data by transaction status"""
        status_summary = {}

        for status in ["SUCCESS", "FAILED"]:
            status_data = df[df["TransactionStatus"] == status]
            status_summary[status] = {
                "count": len(status_data),
                "value": format_currency(status_data["Amount"].sum()),
            }

        return status_summary

    def _analyze_by_type(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """Analyze data by transaction type"""
        type_summary = {}

        for tx_type in ["AIRTIME", "DATA"]:
            type_data = df[df["TransactionType"] == tx_type]
            type_summary[tx_type] = {
                "count": len(type_data),
                "value": format_currency(type_data["Amount"].sum()),
            }

        return type_summary

    def _create_empty_summary(self) -> Dict:
        """Create empty summary for telcos with no data"""
        return {
            "total_transactions": 0,
            "total_value": format_currency(0),
            "by_status": {
                "SUCCESS": {"count": 0, "value": format_currency(0)},
                "FAILED": {"count": 0, "value": format_currency(0)},
            },
            "by_type": {
                "AIRTIME": {"count": 0, "value": format_currency(0)},
                "DATA": {"count": 0, "value": format_currency(0)},
            },
        }


class TelcoReconciliationService:
    """Service for telco reconciliation (Single Responsibility)"""

    def __init__(self):
        self.processor_factory = TelcoProcessorFactory()

    def reconcile_with_telco(
        self,
        telco_name: str,
        hisa_df: pd.DataFrame,
        telco_files: List[bytes],
        target_date: str,
        use_transaction_id: bool = False,
    ) -> Dict:
        """Reconcile HISA data with telco files"""
        try:
            processor = self.processor_factory.create_processor(telco_name)

            # Process telco files
            telco_dfs = []
            for file_content in telco_files:
                if telco_name.upper() == "AIRTEL":
                    df = pd.read_excel(io.BytesIO(file_content), dtype=str)
                else:
                    df = pd.read_excel(io.BytesIO(file_content))
                telco_dfs.append(df)

            # Combine telco files if multiple
            telco_df = pd.concat(telco_dfs) if len(telco_dfs) > 1 else telco_dfs[0]

            # Process telco data
            cleaned_telco = processor.process_telco_data(telco_df)

            if isinstance(cleaned_telco, str):
                return {"error": cleaned_telco}

            # Perform reconciliation
            result = reconcile_logs(
                mno=telco_name.upper(),
                target_date=target_date,
                hisa_admin=hisa_df,
                telco_df=cleaned_telco,
                use_transaction_id=use_transaction_id,
            )

            return {"success": True, "data": result}

        except Exception as e:
            return {"error": f"Error reconciling {telco_name}: {str(e)}"}


class TelcoSummaryService:
    """Main service for telco summary functionality (Open/Closed Principle)"""

    def __init__(self):
        self.hisa_log_processor = HisaLogProcessor()
        self.hisa_analyzer = HisaDataAnalyzer()
        self.reconciliation_service = TelcoReconciliationService()

    def get_user_consumption_details(self, target_date: str, telco: str) -> Dict:
        """Get detailed user consumption data for a specific telco"""
        try:
            # Validate date
            target_date_obj = datetime.strptime(target_date, "%Y-%m-%d").date()

            # Get HISA logs from both sources using optimized method
            hisa_logs = self.hisa_log_processor.get_hisa_logs_for_date_optimized(
                target_date
            )

            # Combine HISA data from both sources
            hisa_dfs = []
            for source, df in hisa_logs.items():
                if not df.empty:
                    df["Source"] = source  # Add source identifier
                    hisa_dfs.append(df)

            if not hisa_dfs:
                return {
                    "error": f"No HISA transaction logs found for date {target_date}. Please ensure logs exist for this date."
                }

            combined_hisa_df = (
                pd.concat(hisa_dfs, ignore_index=True)
                if len(hisa_dfs) > 1
                else hisa_dfs[0]
            )

            # Get user consumption details for the specific telco
            consumption_details = self.hisa_analyzer.analyze_user_consumption_by_telco(
                combined_hisa_df, target_date, telco
            )

            return {
                "target_date": target_date,
                "telco": telco.upper(),
                "consumption_details": consumption_details,
            }

        except Exception as e:
            return {"error": f"Error getting user consumption details: {str(e)}"}

    def generate_summary(
        self,
        target_date: str,
        telco_files: Optional[Dict[str, List[bytes]]] = None,
        use_transaction_id: bool = False,
    ) -> Dict:
        """
        Generate telco summary with or without telco files using HISA logs from SFTP sources

        Args:
            target_date: Target date for analysis
            telco_files: Optional dict of telco files {telco_name: [file_contents]}
            use_transaction_id: Whether to use transaction ID in reconciliation

        Returns:
            Dict containing summary data
        """
        try:
            # Validate date
            target_date_obj = datetime.strptime(target_date, "%Y-%m-%d").date()

            # Get HISA logs from both sources using optimized method
            hisa_logs = self.hisa_log_processor.get_hisa_logs_for_date_optimized(
                target_date
            )

            # Combine HISA data from both sources
            hisa_dfs = []
            for source, df in hisa_logs.items():
                if not df.empty:
                    df["Source"] = source  # Add source identifier
                    hisa_dfs.append(df)

            if not hisa_dfs:
                return {
                    "error": f"No HISA transaction logs found for date {target_date}. Please ensure logs exist for this date."
                }

            combined_hisa_df = (
                pd.concat(hisa_dfs, ignore_index=True)
                if len(hisa_dfs) > 1
                else hisa_dfs[0]
            )

            # Generate HISA-only summary
            hisa_summary = self.hisa_analyzer.analyze_by_telco(
                combined_hisa_df, target_date
            )

            result = {
                "target_date": target_date,
                "hisa_summary": hisa_summary,
                "reconciliation_results": {},
            }

            # If telco files provided, perform reconciliation
            if telco_files:
                for telco_name, files in telco_files.items():
                    if files:  # Only process if files are provided
                        reconciliation_result = (
                            self.reconciliation_service.reconcile_with_telco(
                                telco_name=telco_name,
                                hisa_df=combined_hisa_df,
                                telco_files=files,
                                target_date=target_date,
                                use_transaction_id=use_transaction_id,
                            )
                        )
                        result["reconciliation_results"][
                            telco_name
                        ] = reconciliation_result

            return result

        except Exception as e:
            return {"error": f"Error generating telco summary: {str(e)}"}
